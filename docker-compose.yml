services:
  dcs-postgres:
    image: postgres:15.3-alpine
    container_name: dcs-postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
    ports:
      - 5432:5432
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
  dcs-pgadmin:
    image: dpage/pgadmin4:latest
    container_name: dcs-pgadmin
    restart: always
    depends_on:
      - dcs-postgres
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: password
    ports:
      - 8080:80
    volumes:
      - ./data/pgadmin:/var/lib/pgadmin
    user: "0"

networks:
  default:
    name: local-network

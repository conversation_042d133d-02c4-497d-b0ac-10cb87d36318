import { ClassSerializerInterceptor, Module } from '@nestjs/common';
import { LogradouroModule } from './logradouro/logradouro.module';
import { HealthCheckModule } from './health/health-check.module';
import { DatabaseModule } from './common/database/database.module';
import { RegionModule } from './region/region.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { PartnersSettingsModule } from './partners-settings/partners-settings.module';
import { CurrencyModule } from './currency/currency.module';

@Module({
  imports: [
    DatabaseModule, 
    LogradouroModule,
    RegionModule,
    HealthCheckModule,
    PartnersSettingsModule,
    CurrencyModule,
  ],
  exports: [
    LogradouroModule,
    RegionModule,
    HealthCheckModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ClassSerializerInterceptor,
    },
  ],
})
export class AppModule {}
import { DataSource } from 'typeorm';

export const databaseProviders = [
  {
    provide: 'UTILS_CONNECTION',
    useFactory: () => {
      return new DataSource({
        name: 'utils',
        type: 'postgres',
        host: process.env.DATABASE_HOST,
        port: +process.env.DATABASE_PORT,
        username: process.env.DATABASE_USER,
        password: process.env.DATABASE_PASSWORD,
        database: process.env.DATABASE_DB,
        entities: [__dirname + '/../../**/*.entity{.ts,.js}'],
        synchronize: false,
        ssl: true,
        extra: {
          ssl: {
            rejectUnauthorized: false, 
          },
        },
      });
    },
  },
  {
    provide: 'UTILS_ENTITY_MANAGER',
    useFactory: (utilsDataSource: DataSource) => utilsDataSource.manager,
    inject: ['UTILS_CONNECTION'],
  },
];

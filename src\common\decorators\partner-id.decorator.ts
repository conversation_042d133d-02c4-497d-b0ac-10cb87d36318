import {
  createParamDecorator,
  ExecutionContext,
  BadRequestException,
} from '@nestjs/common';
import { validate as isUuid } from 'uuid';

export const PartnerId = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): string | number => {
    const request = ctx.switchToHttp().getRequest();
    const partnerId = request.headers['partner-id'];

    if (!partnerId) {
      throw new BadRequestException('Header "partner-id" é obrigatório');
    }

    const isUuidValid = isUuid(partnerId);
    const isIntValid = /^\d+$/.test(partnerId);

    if (isUuidValid) {
      return partnerId; // retorna como string UUID
    }

    if (isIntValid) {
      return parseInt(partnerId, 10); // retorna como number
    }

    throw new BadRequestException('Header "partner-id" deve ser um UUID ou número inteiro válido');
  },
);

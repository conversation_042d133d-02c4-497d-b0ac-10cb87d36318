import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class BackofficeGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(req);
    if (!token) {
      throw new HttpException(
        'No authentication header provided.',
        HttpStatus.UNAUTHORIZED
      );
    }

    //Comentado por hora pois o não temos ainda o endpoint para validar de fato o token do backoffice
    //
    // const isValid = await this.isTokenValid(token);
    // if (!isValid) {
    //   throw new HttpException(
    //     'Authentication failed: Token expired or invalid.',
    //     HttpStatus.UNAUTHORIZED
    //   );
    // }

    const payload = JSON.parse(
      Buffer.from(token.split('.')[1], 'base64').toString()
    );

    req.user = payload;
    return true;
  }

  private async isTokenValid(token: string): Promise<boolean> {
    try {
      const url = `${process.env.API_ONBOARDING_TOKEN_URL}/v1/pam/authentication/token/validator`;
      const response = await fetch(url, {
        method: 'POST',
        headers: { Authorization: `Bearer ${token}` },
      });
      const data = await response.json();
      return data.validToken;
    } catch (error) {
      console.error('Erro ao validar token:', error);
      throw new HttpException(
        'Erro ao executar a validação do token',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private extractTokenFromHeader(req: Request): string | null {
    const authorizationHeader = req.headers.authorization;
    if (!authorizationHeader) {
      return null;
    }
    const [bearer, token] = authorizationHeader.split(' ');
    return bearer === 'Bearer' ? token : null;
  }
}

import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { addGlobalResponses } from './swagger-response-global';

export function addSwagger(app: INestApplication<any>) {

  const config = new DocumentBuilder()
    .setTitle("Utils")
    .addBearerAuth(
      {
        description: `Insira apenas o token, sem aspas.`,
        name: "Authorization",
        bearerFormat: "Bearer",
        scheme: "Bearer",
        type: "http",
        in: "Header",
      },
      "access-token"
    )
    .setDescription("The utils API description")
    .setVersion("1.0")
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Adicionando respostas globais
  addGlobalResponses(document);
    
  SwaggerModule.setup("/v1/pam/utils/doc", app, document);
}

import { HttpStatus } from '@nestjs/common';
import { OpenAPIObject } from '@nestjs/swagger';

export function addGlobalResponses(document: OpenAPIObject) {
  Object.keys(document.paths).forEach((path) => {
    Object.keys(document.paths[path]).forEach((method) => {
      document.paths[path][method].responses = {
        '204': {
          description: 'No Content.',
          content: {
            'application/json': {
              example: {
                statusCode: HttpStatus.NO_CONTENT,
                message: 'No Content.',
              }
            }
          }
        },
        '400': {
          description: 'Bad Request.',
          content: {
            'application/json': {
              example: {
                statusCode: HttpStatus.BAD_REQUEST,
                message: 'Bad Request.',
              }
            }
          }
        },
        '401': {
          description: 'Unauthorized.',
          content: {
            'application/json': {
              example: {
                statusCode: HttpStatus.UNAUTHORIZED,
                message: 'Unauthorized.',
              }
            }
          }
        },
        '403': {
          description: 'Forbidden.',
          content: {
            'application/json': {
              example: {
                statusCode: HttpStatus.FORBIDDEN,
                message: 'Forbidden.',
              }
            }
          }
        },
        '404': {
          description: 'Not Found.',
          content: {
            'application/json': {
              example: {
                statusCode: HttpStatus.NOT_FOUND,
                message: 'Not Found',
              }
            }
          }
        },
        '409': {
          description: 'Conflict.',
          content: {
            'application/json': {
              example: {
                statusCode: HttpStatus.CONFLICT,
                message: 'Conflict.',
              }
            }
          }
        },
        '500': {
          description: 'Internal Server Error.',
          content: {
            'application/json': {
              example: {
                statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                message: 'Internal Server Error.',
              }
            }
          }
        },
        ...document.paths[path][method].responses,
      };
    });
  });
};

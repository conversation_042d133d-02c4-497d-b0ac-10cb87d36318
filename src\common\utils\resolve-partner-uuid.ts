import { BadRequestException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { validate as isUuid } from 'uuid';
import { PamPartnersOpah } from '@/partners-settings/entities/pam-partners-opah.entity';

/**
 * Resolve o UUID do parceiro a partir de um ID (UUID ou número).
 * 
 * @param partnerId - UUID (string) ou ID numérico (number ou string numérica)
 * @param pamRepo - Repositório de parceiros (PamPartnersOpah)
 * @returns UUID (string) do parceiro
 * @throws BadRequestException - se o ID for inválido ou o parceiro não existir
 */
export async function resolvePartnerUuid(
  partnerId: string | number,
  pamRepo: Repository<PamPartnersOpah>
): Promise<string> {
  if (typeof partnerId === 'string' && isUuid(partnerId)) {
    return partnerId;
  }

  const isIntValid =
    typeof partnerId === 'number' || /^\d+$/.test(partnerId.toString());

  if (!isIntValid) {
    throw new BadRequestException(
      'Header "partner-id" deve ser um UUID ou número inteiro válido',
    );
  }

  const idAsNumber = Number(partnerId);
  const partner = await pamRepo.findOneBy({ idPartner: idAsNumber });

  if (!partner) {
    throw new BadRequestException(
      `Parceiro com idPartner ${partnerId} não encontrado`,
    );
  }

  return partner.id; // UUID real usado no relacionamento
}

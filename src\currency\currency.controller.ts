import { Controller, Get, UseGuards } from '@nestjs/common';
import { CurrencyService } from './currency.service';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { CurrencyDto } from './dto/currency.dto';

@ApiTags('Currency')
@Controller('')
@ApiBearerAuth('access-token')
@UseGuards(BackofficeGuard)
export class CurrencyController {
  constructor(private readonly currencyService: CurrencyService) { }

  @Get('currency/all')
  @ApiOperation({ summary: 'Busca todas as moedas' })
  @ApiResponse({ status: 200, description: 'Retorna todas as moedas.', type: [CurrencyDto] })
  async getCurrencyAll() {
    return await this.currencyService.getCurrencyAll();
  }

  @Get('wallet/currency/distinct')
  @ApiOperation({ summary: 'Busca as moedas distintas' })
  @ApiResponse({ status: 200, description: 'Retorna as moedas distintas.', type: [CurrencyDto] })
  async getCurrencyDistinct() {
    return await this.currencyService.getCurrencyDistinct();
  }
}

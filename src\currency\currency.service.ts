import { Region } from "@/region/entities/region.entity";
import { HttpException, Injectable, Logger } from "@nestjs/common";
import { InjectEntityManager } from "@nestjs/typeorm";
import { EntityManager, IsNull, Not } from "typeorm";
import { CurrencyDto } from "./dto/currency.dto";

@Injectable()
export class CurrencyService {
  private readonly logger = new Logger(CurrencyService.name);

  constructor(@InjectEntityManager() private readonly manager: EntityManager) { }

  async getCurrencyAll(): Promise<CurrencyDto[]> {
    try {
      const currencies = await this.manager.find(Region, {
        where: { currencyCode: Not(IsNull()) },
      });

      return currencies.map((currency) => ({
        id: currency.id,
        currencyCode: currency.currencyCode,
        currencyName: currency.currencyName,
        currencySymbol: currency.currencySymbol,
      } as CurrencyDto));
    } catch (error) {
      this.logger.error("Error fetching currencies:", error);
      throw new HttpException("Failed to fetch currencies", 500);
    }
  }

  async getCurrencyDistinct(): Promise<CurrencyDto[]> {
    try {
      const currencies = await this.manager
        .createQueryBuilder()
        .select([
          "DISTINCT r.id AS id",
          "r.currency_code AS currencyCode",
          "r.currency_name AS currencyName",
          "r.currency_symbol AS currencySymbol",
        ])
        .from("wallet.wallet_wallet", "wallet_wallet")
        .innerJoin("region", "r", "wallet_wallet.region_id = r.id")
        .getRawMany();

      return currencies as CurrencyDto[];
    } catch (error) {
      this.logger.error("Error fetching distinct currencies:", error);
      throw new HttpException("Failed to fetch distinct currencies", 500);
    }
  }
}

import { ApiProperty } from "@nestjs/swagger";
export class C<PERSON>rencyDto {
  @ApiProperty({
    description: "Identificador da moeda.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-426614174000",
    required: true,
  })  
  id: string;

  @ApiProperty({
    description: "Código da moeda.",
    type: "string",
    example: "BRL",
    required: true,
  })  
  currencyCode: string;

  @ApiProperty({
    description: "Nome da moeda.",
    type: "string",
    example: "Real",
    required: true,
  })  
  currencyName: string;

  @ApiProperty({
    description: "Símbolo da moeda.",
    type: "string",
    example: "R$",
    required: true,
  })  
  currencySymbol: string;
}



// wallet.entity.ts
import { Region } from "@/region/entities/region.entity";
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Unique,
  Index,
  JoinColumn,
  OneToOne,
} from "typeorm";

@Entity("wallet_wallet", { schema: "wallet" }) // Define o nome da tabela e o esquema
@Unique(["partnerId", "playerId"]) // Garante unicidade para a combinação de parceiro e jogador
@Index(["partnerId", "playerId"]) // Índice para melhorar buscas por partnerId e playerId
export class Wallet {
  @PrimaryGeneratedColumn("uuid", { name: "id" })
  id: string;

  @Column({ name: "partner_id" })
  partnerId: string;

  @Column({ name: "player_id" })
  playerId: string;

  @Column({
    name: "wallet_number",
    type: "bigint",
    unique: true,
  })
  walletNumber: number;

  @Column({ name: "currency", type: "varchar", length: 3 })
  currency: string;

  @Column({ name: "email" })
  email: string;

  @Column({ name: "region_id", nullable: true })
  regionId: string;

  @OneToOne(() => Region)
  @JoinColumn({ name: "region_id" })
  region: Region;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @DeleteDateColumn({ name: "deleted_at" })
  deletedAt: Date;

  @Column({ name: "is_deleted" })
  isDeleted: boolean;
}

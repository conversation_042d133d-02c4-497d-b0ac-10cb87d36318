import { ApiProperty } from '@nestjs/swagger';

class database {
  @ApiProperty({
    description: 'Status of the database',
    example: 'UP',
  })
  status: string;

  @ApiProperty({
    description: 'Response time in ms',
    example: '10',
  })
  response_time_ms: number
}

class dependencies {
  @ApiProperty({
    description: 'Database',
  })
  database: database
}

export class healthCheckDto {
  @ApiProperty({
    description: 'Status',
    example: 'UP',
  })
  status: string;
  @ApiProperty({
    description: 'Timestamp',
    example: '2025-03-11T21:05:50.280Z"',
  })
  timestamp: string;
  @ApiProperty({
    description: 'Version',
    example: '1.0.0',
  })
  version: string;

  @ApiProperty({
    description: 'Uptime in seconds',
    example: '98.1823744',
  })
  uptime_seconds: number;

  @ApiProperty()
  dependencies: dependencies
}
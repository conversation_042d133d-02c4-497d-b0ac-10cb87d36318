import { Controller, Get } from '@nestjs/common';
import { HealthCheckService } from './health-check.service';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { healthCheckDto } from './dto/health-check.dto';

@ApiTags('Health-Check')
@Controller('health-check')
export class HealthCheckController {
  constructor(private readonly healthCheckService: HealthCheckService) { }

  @Get()
  @ApiOperation({ summary: 'Busca o status de saúde da API.' })
  @ApiResponse({ status: 200, description: 'Returned health.', type: healthCheckDto })
  async checkHealth() {
    return this.healthCheckService.getHealthStatus();
  }
}

import { Injectable } from "@nestjs/common";
import { EntityManager } from "typeorm";
import { join } from "path";
import { readFileSync } from "fs";
import { healthCheckDto } from "./dto/health-check.dto";
import { InjectEntityManager } from "@nestjs/typeorm";

@Injectable()
export class HealthCheckService {
  constructor(
    @InjectEntityManager() private readonly utilsManager: EntityManager
  ) {}

  async getHealthStatus(): Promise<healthCheckDto> {
    const start = Date.now();
    let dbStatus = "OK";
    let dbResponseTime = 0;

    try {
      await this.utilsManager.query("SELECT 1");
      dbResponseTime = Date.now() - start;
    } catch (error) {
      dbStatus = "DOWN";
      dbResponseTime = -1;
    }

    const packageJsonPath = join(__dirname, "..", "..", "package.json");
    const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf-8"));
    const version = packageJson.version as string;

    return {
      status: "UP",
      timestamp: new Date().toISOString(),
      version,
      uptime_seconds: process.uptime(),
      dependencies: {
        database: {
          status: dbStatus,
          response_time_ms: dbResponseTime,
        },
      },
    };
  }
}

import {
  Column,
  Entity,  
  PrimaryGeneratedColumn,  
} from "typeorm";
import { ApiProperty } from '@nestjs/swagger';

@Entity("logradouro")
export class Logradouro {
  @PrimaryGeneratedColumn("increment", { name: "id_logradouro" })
  id: number;

  @ApiProperty({
    description: 'Cep do logradouro',
    example: '01311200',
  })
  @Column({ name: "cep" })
  cep: string;

  @ApiProperty({
    description: 'Estado',
    example: 'SP',
  })
  @Column({ name: "state" })
  state: string;

  @ApiProperty({
    description: 'Cidade',
    example: 'São Paulo',
  })
  @Column({ name: "city", nullable: true })
  city: string;

  @ApiProperty({
    description: 'Bairro',
    example: 'Bela Vista',
  })
  @Column({ name: "neighborhood", nullable: true })
  neighborhood: string;

  @ApiProperty({
    description: 'Logradouro',
    example: 'Avendia Paulista',
  })
  @Column({ name: "street" })
  street: string;

}

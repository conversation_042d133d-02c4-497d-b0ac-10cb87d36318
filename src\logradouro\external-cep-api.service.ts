import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ExternalCepDto } from './dto/external-cep.dto';

@Injectable()
export class ExternalCepApiService {
  private apiExternalUrl = process.env.API_EXTERNAL_URL;
  private readonly logger = new Logger(ExternalCepApiService.name);

  constructor(private readonly httpService: HttpService) { }

  async findCep(cep: string): Promise<ExternalCepDto> {
    try {
      this.logger.log(`[Start] Find by cep in External Api: ${cep}`);

      var response = await firstValueFrom(this.httpService.get(`${this.apiExternalUrl}/${cep}`));
      if (response.status !== 200)
        return null;
      
      const data = response.data;

      this.logger.log(`[End] Find by cep in External Api: ${cep}`);
      return {
        cep: data.cep,
        state: data.state,
        city: data.city,
        neighborhood: data.neighborhood,
        street: data.street,
      } as ExternalCepDto;

    } catch (error) {
      this.logger.error(`Error finding logradouro in external api: ${error.response.status}-${error.response.statusText}`);
    }
  }
}
import { Controller, Get, Param, HttpCode } from '@nestjs/common';
import { LogradouroService } from './logradouro.service';
import { Logradouro } from './entities/logradouro.entity';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Logradouro')
@Controller('logradouro')

export class LogradouroController {
  constructor(private readonly logradouroService: LogradouroService) { }

  @Get(':cep')
  @ApiOperation({ summary: 'Busca o logradouro pelo cep.' })
  @ApiResponse({ status: 200, description: 'Returned by cep logradouro.', type: Logradouro })
  @ApiParam({ name: 'cep', type: String, description: 'Cep do logradouro' })
  findOne(@Param('cep') cep: string): Promise<Logradouro> {
    return this.logradouroService.findByCep(cep);
  }
}

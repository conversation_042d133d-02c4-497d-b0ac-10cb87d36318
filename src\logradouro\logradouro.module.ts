import { Module } from '@nestjs/common';
import { LogradouroService } from './logradouro.service';
import { LogradouroController } from './logradouro.controller';
import { ExternalCepApiService } from './external-cep-api.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [HttpModule],
  controllers: [LogradouroController],
  providers: [LogradouroService, ExternalCepApiService],
})
export class LogradouroModule { }

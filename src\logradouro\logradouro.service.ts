import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { InjectEntityManager } from "@nestjs/typeorm";
import { EntityManager } from "typeorm";
import { Logradouro } from "./entities/logradouro.entity";
import { ExternalCepApiService } from "./external-cep-api.service";

@Injectable()
export class LogradouroService {
  private readonly logger = new Logger(LogradouroService.name);
  constructor(
    @InjectEntityManager() private readonly utilsManager: EntityManager,
    private readonly externalCepApiService: ExternalCepApiService
  ) {}

  async findByCep(cep: string): Promise<Logradouro> {
    try {
      this.logger.log(`[Start] Find by cep logradouro: ${cep}`);

      if (!this.validateCep(cep)) {
        this.logger.warn(`Invalid CEP: ${cep}`);
        throw new HttpException("Invalid CEP", HttpStatus.BAD_REQUEST);
      }

      let logradouro = await this.utilsManager.findOne(Logradouro, {
        where: { cep },
      });

      if (logradouro) {
        this.logger.log(`[End] Find by cep logradouro: ${cep}`);
        return logradouro;
      }

      const externalCep = await this.externalCepApiService.findCep(cep);
      if (externalCep) {
        logradouro = new Logradouro();
        logradouro.cep = externalCep.cep;
        logradouro.state = externalCep.state;
        logradouro.city = externalCep.city;
        logradouro.neighborhood = externalCep.neighborhood;
        logradouro.street = externalCep.street;
        logradouro = await this.utilsManager.save(Logradouro, logradouro);
      }

      if (!logradouro) {
        this.logger.warn(`No records found`);
        throw new HttpException("No records found", HttpStatus.NO_CONTENT);
      }

      this.logger.log(`[End] Find by cep logradouro: ${cep}`);
      return logradouro;
    } catch (err) {
      this.logger.error(`Error finding logradouro: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  validateCep(cep: string): boolean {
    const cleanCep = cep.replace(/\D/g, ""); // Remove all non-digit characters
    return cleanCep.length === 8;
  }
}

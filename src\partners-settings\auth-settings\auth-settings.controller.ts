import {
  Controller,
  Get,
  Inject,
  Post,
  Patch,
  Body,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiHeader,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiConflictResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { PartnerId } from '@/common/decorators/partner-id.decorator';
import { AuthSettingsService } from './auth-settings.service';
import { CreateOrUpdateAuthSettingsDto } from '../dto/create-update-auth-settings.dto';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { BackofficeUser } from '@/common/decorators/backoffice-user.decorator';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { AuthSettings } from '../entities/auth-settings.entity';

@Controller('backoffice/partners/auth-settings')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@ApiTags('Settings Partners Auth')
export class AuthSettingsController {
  @Inject() private readonly authSettingsService: AuthSettingsService;

  @Get()
  @ApiOperation({
    summary: 'Busca as configurações de autenticação do parceiro',
  })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiOkResponse({ type: [AuthSettings] })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async findAll(@PartnerId() partnerId: string): Promise<AuthSettings[]> {
    const result = await this.authSettingsService.findAll(partnerId);
    return result;
  }

  @Post()
  @ApiOperation({
    summary: 'Cria configurações de autenticação para o parceiro',
  })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateAuthSettingsDto })
  @ApiCreatedResponse({ type: AuthSettings })
  @ApiConflictResponse({ description: 'Configurações já existentes' })
  async create(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateAuthSettingsDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ): Promise<AuthSettings> {
    return await this.authSettingsService.create(
      partnerId,
      body,
      backofficeUser,
    );
  }

  @Patch()
  @ApiOperation({
    summary: 'Atualiza configurações de autenticação do parceiro',
  })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateAuthSettingsDto })
  @ApiOkResponse({ type: AuthSettings })
  @ApiBadRequestResponse({ description: 'Campos inválidos' })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async update(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateAuthSettingsDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ): Promise<AuthSettings> {
    return await this.authSettingsService.update(
      partnerId,
      body,
      backofficeUser,
    );
  }
}

import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { AuthSettings } from '../entities/auth-settings.entity';
import { CreateOrUpdateAuthSettingsDto } from '../dto/create-update-auth-settings.dto';
import { PartnerHistoric } from '../entities/partner-historic.entity';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';

@Injectable()
export class AuthSettingsService {
  constructor(
    @InjectRepository(AuthSettings, 'partners')
    private readonly repository: Repository<AuthSettings>,

    @InjectRepository(PartnerHistoric, 'partners')
    private readonly partnerHistoricRepository: Repository<PartnerHistoric>,

    @InjectDataSource('partners')
    private readonly dataSource: DataSource,
  ) {}

  async findAll(partnerId: string): Promise<AuthSettings[]> {
    return this.repository.find({
      where: { idPartner: partnerId, deletedAt: null },
    });
  }

  async create(
    partnerId: string,
    dto: CreateOrUpdateAuthSettingsDto,
    backofficeUser: IBackofficeUser,
  ): Promise<AuthSettings> {
    const exists = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });
    if (exists) throw new ConflictException();
    return this.dataSource.transaction(async (manager) => {
      const repo = manager.getRepository(AuthSettings);
      const historicRepo = manager.getRepository(PartnerHistoric);

      const entity = repo.create({
        idPartner: partnerId,
        ...dto,
      });
      const activity = historicRepo.create({
        createdAt: new Date(),
        category: 'general',
        partnerId,
        payloadNew: dto,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId,
      });
      await historicRepo.save(activity);
      return await repo.save(entity);
    });
  }

  async update(
    partnerId: string,
    dto: CreateOrUpdateAuthSettingsDto,
    backofficeUser: IBackofficeUser,
  ): Promise<AuthSettings> {
    const existing = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });
    if (!existing) throw new NotFoundException();

    return this.dataSource.transaction(async (manager) => {
      const repo = manager.getRepository(AuthSettings);
      const historicRepo = manager.getRepository(PartnerHistoric);
      const oldPayload = { ...existing };

      Object.assign(existing, {
        ...dto,
        updated_at: new Date(),
      });

      const activity = historicRepo.create({
        createdAt: new Date(),
        category: 'auth',
        partnerId,
        payloadOld: oldPayload,
        payloadNew: dto,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId,
      });
      await historicRepo.save(activity);
      return await repo.save(existing);
    });
  }
}

import {
  Controller,
  Get,
  Inject,
  Post,
  Patch,
  Body,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiHeader,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiConflictResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { DepositSettingsService } from './deposit-settings.service';
import { CreateOrUpdateDepositSettingsDto } from '../dto/create-update-deposit-settings.dto';
import { PartnerId } from '@/common/decorators/partner-id.decorator';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { BackofficeUser } from '@/common/decorators/backoffice-user.decorator';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { DepositSettings } from '../entities/deposit-settings.entity';

@Controller('backoffice/partners/deposit-settings')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@ApiTags('Settings Partners Deposit')
export class DepositSettingsController {
  @Inject() private readonly depositSettingsService: DepositSettingsService;

  @Get()
  @ApiOperation({ summary: 'Busca as configurações de depósito do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiOkResponse({ type: [DepositSettings] })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async findAll(@PartnerId() partnerId: string) {
    const result = await this.depositSettingsService.findAll(partnerId);
    return result;
  }

  @Post()
  @ApiOperation({ summary: 'Cria configurações de depósito para o parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateDepositSettingsDto })
  @ApiCreatedResponse({ type: DepositSettings })
  @ApiConflictResponse({ description: 'Configurações já existentes' })
  async create(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateDepositSettingsDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    return await this.depositSettingsService.create(
      partnerId,
      body,
      backofficeUser,
    );
  }

  @Patch()
  @ApiOperation({ summary: 'Atualiza configurações de depósito do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateDepositSettingsDto })
  @ApiOkResponse({ type: DepositSettings })
  @ApiBadRequestResponse({ description: 'Campos inválidos' })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async update(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateDepositSettingsDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    return await this.depositSettingsService.update(
      partnerId,
      body,
      backofficeUser,
    );
  }
}

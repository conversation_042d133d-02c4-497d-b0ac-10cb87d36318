import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { DepositSettings } from '../entities/deposit-settings.entity';
import { CreateOrUpdateDepositSettingsDto } from '../dto/create-update-deposit-settings.dto';
import { PartnerHistoric } from '../entities/partner-historic.entity';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';

@Injectable()
export class DepositSettingsService {
  constructor(
    @InjectRepository(DepositSettings, 'partners')
    private readonly repository: Repository<DepositSettings>,
    @InjectDataSource('partners')
    private readonly dataSource: DataSource,
  ) { }

  async findAll(partnerId: string): Promise<DepositSettings[]> {
    return this.repository.find({
      where: { idPartner: partnerId, deletedAt: null },
    });
  }

  async create(
    partnerId: string,
    dto: CreateOrUpdateDepositSettingsDto,
    backofficeUser: IBackofficeUser,
  ): Promise<DepositSettings> {
    const exists = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (exists) {
      throw new ConflictException(
        'Configurações já existem para este parceiro.',
      );
    }
    return this.dataSource.transaction(async (manager) => {
      const entity = manager.getRepository(DepositSettings).create({
        idPartner: partnerId,
        ...dto,
      });

      const createPartnerActivity = manager
        .getRepository(PartnerHistoric)
        .create({
          createdAt: new Date(),
          category: 'general',
          partnerId: partnerId,
          payloadNew: dto,
          createdBy: backofficeUser.email,
          createdById: backofficeUser.userId,
        });

      await manager.getRepository(PartnerHistoric).save(createPartnerActivity);
      return await manager.getRepository(DepositSettings).save(entity);
    });
  }

  async update(
    partnerId: string,
    dto: CreateOrUpdateDepositSettingsDto,
    backofficeUser: IBackofficeUser,
  ): Promise<DepositSettings> {
    const existing = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (!existing) {
      throw new NotFoundException('Configurações de depósito não encontradas.');
    }
    return this.dataSource.transaction(async (manager) => {
      Object.assign(existing, {
        ...dto,
        updated_at: new Date(),
      });

      const createPartnerActivity = manager
        .getRepository(PartnerHistoric)
        .create({
          createdAt: new Date(),
          category: 'deposit',
          partnerId: partnerId,
          payloadOld: existing,
          payloadNew: dto,
          createdBy: backofficeUser.email,
          createdById: backofficeUser.userId,
        });

      await manager.getRepository(PartnerHistoric).save(createPartnerActivity);
      return await manager.getRepository(DepositSettings).save(existing);
    });
  }
}

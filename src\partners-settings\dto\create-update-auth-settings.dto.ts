import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsOptional, Min } from 'class-validator';

export class CreateOrUpdateAuthSettingsDto {
  @ApiProperty({
    description: "Número máximo de tentativas de login antes de bloquear o acesso.",
    type: "number",
    example: 7,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  loginAttemptLimit?: number;

  @ApiProperty({
    description: "Número de minutos que o acesso será bloqueado após atingir o limite de tentativas de login.",
    type: "number",
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  loginBlockMinutes?: number;

  @ApiProperty({
    description: "Verifica a data de nascimento do usuário ao fazer login.",
    type: "boolean",
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  verifyBirthdateOnlogin?: boolean;

  @ApiProperty({
    description: "Restringe o login a apenas o email do usuário.",
    type: "boolean",
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  restrictLoginByEmail?: boolean;

  @ApiProperty({
    description: "Habilita a autenticação de dois fatores.",
    type: "boolean",
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enable2fa?: boolean;

  @ApiProperty({
    description: "Permite apenas um login ativo por usuário.",
    type: "boolean",
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  uniqueLogin?: boolean;

  @ApiProperty({
    description: "Impede que um usuário use a mesma senha que usou nos últimos 'n' dias.",
    type: "boolean",
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enforcePasswordHistory?: boolean;

  @ApiProperty({
    description: "Número de senhas anteriores que não podem ser reutilizadas.",
    type: "number",
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  passwordHistoryLimit?: number;
}

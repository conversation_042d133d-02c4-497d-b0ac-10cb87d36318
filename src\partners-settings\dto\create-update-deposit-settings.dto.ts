import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsInt, Min, IsBoolean } from 'class-validator';

export class CreateOrUpdateDepositSettingsDto {
  @ApiProperty({
    description: "Valor mínimo de depósito.",
    type: "number",
    example: 3.00,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  minDepositAmount?: number;

  @ApiProperty({
    description: "Valor máximo de depósito.",
    type: "number",
    example: 50000.00,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  maxDepositAmount?: number;

  @ApiProperty({
    description: "Limite mínimo para ativar a resposta automática.",
    type: "number",
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  autoResponseThreshold?: number;

  @ApiProperty({
    description: "Ha<PERSON>ita o preenchimento automático do template.",
    type: "boolean",
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  autoFillTemplateEnabled?: boolean;

  @ApiProperty({
    description: "Habilita a resposta automática.",
    type: "boolean",
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  autoResponseEnabled?: boolean;
}
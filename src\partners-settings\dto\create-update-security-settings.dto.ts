import { IsOptional, IsInt, IsString, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOrUpdateSecuritySettingsDto {
  @ApiProperty({
    description: "Número de dias para expirar a senha.",
    type: "number",
    example: 90,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  passwordExpirationDays?: number;

  @ApiProperty({
    description: "Número de dias para expirar a senha temporária.",
    type: "number",
    example: 7,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  temporaryPasswordExpirationDays?: number;

  @ApiProperty({
    description: "<PERSON><PERSON><PERSON> mínimo da senha.",
    type: "number",
    example: 8,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(4)
  minPasswordLength?: number;

  @ApiProperty({
    description: "Expressão regular para validar a complexidade da senha.",
    type: "string",
    example: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$',
    required: false,
  })
  @IsOptional()
  @IsString()
  passwordRegex?: string;

  @ApiProperty({
    description: "Número máximo de tentativas de senha antes de bloquear o acesso.",
    type: "number",
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  passwordAttemptLimit?: number;

  @ApiProperty({
    description: "Número de dias para notificar o usuário sobre a expiração da senha.",
    type: "number",
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  passwordExpirationNoticeDays?: number;

  @ApiProperty({
    description: "Número de minutos que o acesso será bloqueado após atingir o limite de tentativas de senha.",
    type: "number",
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  loginBlockMinutes: number;
}

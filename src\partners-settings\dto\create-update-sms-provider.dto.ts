import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsInt,
  IsUrl,
  IsPhoneNumber,
} from 'class-validator';

export class CreateOrUpdateSmsProviderDto {
  @ApiProperty({
    description: "Nome do provedor de SMS.",
    type: "string",
    example: "Twilio",
    required: false,
  })
  @IsOptional()
  @IsString()
  providerName?: string;

  @ApiProperty({
    description: "URL do provedor de SMS.",
    type: "string",
    example: "https://api.twilio.com/",
    required: false,
  })
  @IsOptional()
  @IsUrl()
  providerUrl?: string;

  @ApiProperty({
    description: "URL de verificação de status do provedor de SMS.",
    type: "string",
    example: "https://api.twilio.com/status",
    required: false,
  })
  @IsOptional()
  @IsUrl()
  statusCheckUrl?: string;

  @ApiProperty({
    description: "URL de verificação de saldo do provedor de SMS.",
    type: "string",
    example: "https://api.twilio.com/balance",
    required: false,
  })
  @IsOptional()
  @IsUrl()
  balanceCheckUrl?: string;

  @ApiProperty({
    description: "Nome de usuário para autenticação no provedor de SMS.",
    type: "string",
    example: "user-123",
    required: false,
  })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({
    description: "Senha para autenticação no provedor de SMS.",
    type: "string",
    example: "secret-password",
    required: false,
  })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: "Chave de API para envio de SMS.",
    type: "string",
    example: "key-abc123",
    required: false,
  })
  @IsOptional()
  @IsString()
  smsApiKey?: string;

  @ApiProperty({
    description: "Segredo da chave de API para envio de SMS.",
    type: "string",
    example: "secret-xyz",
    required: false,
  })
  @IsOptional()
  @IsString()
  smsApiSecret?: string;

  @ApiProperty({
    description: "Token de acesso para envio de SMS.",
    type: "string",
    example: "token-001",
    required: false,
  })
  @IsOptional()
  @IsString()
  smsToken?: string;

  @ApiProperty({
    description: "Número de origem para envio de SMS.",
    type: "string",
    example: "+17172826936",
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber(null)
  fromNumber?: string;

  @ApiProperty({
    description: "Tempo de espera em segundos para envio de SMS.",
    type: "number",
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsInt()
  timeoutSeconds?: number;

  @ApiProperty({
    description: "Rota para envio de SMS.",
    type: "string",
    example: "/sms/send",
    required: false,
  })
  @IsOptional()
  @IsString()
  route?: string;
}

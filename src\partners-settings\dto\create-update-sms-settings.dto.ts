import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, IsArray, IsUUID } from 'class-validator';

export class CreateOrUpdateSmsSettingDto {
  @ApiProperty({
    description: "Tempo de expiração do código de SMS em segundos.",
    type: "number",
    example: 300,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  codeExpirationSeconds?: number;

  @ApiProperty({
    description: "Tamanho do código de SMS.",
    type: "number",
    example: 4,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  codeLength?: number;

  @ApiProperty({
    description: "Identificadores das regiões para restrição de envio de SMS.",
    type: "array",
    example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"],
    required: false,
  })

  @IsOptional()
  @IsArray()
  @IsUUID()
  regionIds?: string[];
}

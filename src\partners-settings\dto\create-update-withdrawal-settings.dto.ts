import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, IsNumber } from 'class-validator';

export class CreateOrUpdateWithdrawalSettingsDto {
  @ApiProperty({
    description: "Número máximo de solicitações de retirada por dia.",
    type: "number",
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxRequestsPerDay?: number;

  @ApiProperty({
    description: "Valor mínimo de retirada.",
    type: "number",
    example: 10.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  minWithdrawalAmount?: number;

  @ApiProperty({
    description: "Valor máximo de retirada.",
    type: "number",
    example: 50000.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  maxWithdrawalAmount?: number;

  @ApiProperty({
    description: "Número máximo de solicitações pendentes.",
    type: "number",
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxPendingRequests?: number;

  @ApiProperty({
    description: "Valor máximo de retirada paga por dia.",
    type: "number",
    example: 250000.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  maxPaidAmountPerDay?: number;

  @ApiProperty({
    description: "Número máximo de solicitações pagas por dia.",
    type: "number",
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxPaidRequestsPerDay?: number;
}

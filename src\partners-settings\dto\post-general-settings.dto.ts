import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class CreateGeneralSettingsPayloadDto {
  @ApiProperty({
    description: "Descrição do parceiro.",
    type: "string",
    example: "SupremaBet",
    required: true,
  })
  @IsOptional()
  @IsString()
  descriptionPartner: string;

  @ApiProperty({
    description: "Endereço do parceiro.",
    type: "string",
    example: "Rua da cidade, 18",
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: "Telefone do parceiro.",
    type: "string",
    example: "219999-9999",
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: "Email do parceiro.",
    type: "string",
    example: "<EMAIL>",
    required: false,
  })
  @IsOptional()
  @IsString()
  email?: string;
}
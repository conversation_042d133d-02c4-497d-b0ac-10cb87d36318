import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class PutGeneralSettingsResponseDto {
  @ApiProperty({
    description: "Identificador da configuração.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-426614174000",
    required: true,
  })
  id: string;

  @ApiProperty({
    description: "Descrição do parceiro.",
    type: "string",
    example: "SupremaBet",
    required: true,
  })
  descriptionPartner: string;

  @ApiProperty({
    description: "Endereço do parceiro.",
    type: "string",
    example: "Rua da cidade, 18",
    required: false,
  })
  address?: string;

  @ApiProperty({
    description: "Telefone do parceiro.",
    type: "string",
    example: "219999-9999",
    required: false,
  })
  phone?: string;

  @ApiProperty({
    description: "Email do parceiro.",
    type: "string",
    example: "<EMAIL>",
    required: false,
  })
  email?: string;
}

export class PutGeneralSettingsPayloadDto {
  @ApiProperty({
    description: "Descrição do parceiro.",
    type: "string",
    example: "SupremaBet",
    required: false,
  })
  @IsOptional()
  @IsString()
  descriptionPartner: string;

  @ApiProperty({
    description: "Endereço do parceiro.",
    type: "string",
    example: "Rua da cidade, 18",
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: "Telefone do parceiro.",
    type: "string",
    example: "219999-9999",
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: "Email do parceiro.",
    type: "string",
    example: "<EMAIL>",
    required: false,
  })
  @IsOptional()
  @IsString()
  email?: string;
}
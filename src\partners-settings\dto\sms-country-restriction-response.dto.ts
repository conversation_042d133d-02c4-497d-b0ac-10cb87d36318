import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

export class SmsCountryRestrictionResponseDto {
  @ApiProperty({
    description: "Identificador da restrição de país.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-426614174000",
    required: true,
  })
  id: string;

  @ApiProperty({
    description: "Identificador da configuração de SMS.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-426614174000",
    required: true,
  })
  idSmsSetting: string;

  @ApiProperty({
    description: "Identificador da região.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-426614174000",
    required: true,
  })
  idRegion: string;

  @ApiProperty({
    description: "Nome da região.",
    type: "string",
    example: "Europe",
    required: true,
  })
  regionName: string;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @Transform(({ value }) =>
    value ? dayjs(value).tz('America/Sao_Paulo').format() : value
  )
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @Transform(({ value }) =>
    value ? dayjs(value).tz('America/Sao_Paulo').format() : value
  )
  updatedAt: Date | null;
}

import { ApiProperty } from '@nestjs/swagger';
import { SmsCountryRestrictionResponseDto } from './sms-country-restriction-response.dto';
import { Transform } from 'class-transformer';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

export class SmsSettingResponseDto {
  @ApiProperty({
    description: "Identificador da configuração de SMS.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-426614174000",
    required: true,
  })
  id: string;

  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-426614174000",
    required: true,
  })
  idPartner: string;

  @ApiProperty({
    description: "Tempo de expiração do código de SMS em segundos.",
    type: "number",
    example: 300,
    required: true,
  })
  codeExpirationSeconds: number;

  @ApiProperty({
    description: "Tamanho do código de SMS.",
    type: "number",
    example: 4,
    required: true,
  })
  codeLength: number;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @Transform(({ value }) =>
    value ? dayjs(value).tz('America/Sao_Paulo').format() : value
  )
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @Transform(({ value }) =>
    value ? dayjs(value).tz('America/Sao_Paulo').format() : value
  )
  updatedAt: Date | null;

  @ApiProperty({
    description: "Restrições de país para envio de SMS.",
    type: "array",
    required: false,
  })
  countryRestrictions: SmsCountryRestrictionResponseDto[];
}

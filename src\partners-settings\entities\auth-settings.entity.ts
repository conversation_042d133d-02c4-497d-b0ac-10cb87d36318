import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity({ name: 'partner_auth_settings', schema: 'partners' })
export class AuthSettings {
  @ApiProperty({
    description: "Identificador da configuração de autenticação.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @Column({ name: 'id_partner', type: 'uuid' })
  idPartner: string;

  @ApiProperty({
    description: "Número máximo de tentativas de login antes de bloquear o acesso.",
    type: "number",
    example: 7,
    required: false,
  })
  @Column({ name: 'login_attempt_limit', type: 'int', nullable: true })
  loginAttemptLimit: number;

  @ApiProperty({
    description: "Número de minutos que o acesso será bloqueado após atingir o limite de tentativas de login.",
    type: "number",
    example: 10,
    required: false,
  })
  @Column({ name: 'login_block_minutes', type: 'int', nullable: true })
  loginBlockMinutes: number;

  @ApiProperty({
    description: "Verifica a data de nascimento do usuário ao fazer login.",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column({
    name: 'verify_birthdate_on_login',
    type: 'boolean',
    nullable: true,
  })
  verifyBirthdateOnLogin: boolean;

  @ApiProperty({
    description: "Restringe o login a apenas o email do usuário.",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column({ name: 'restrict_login_by_email', type: 'boolean', nullable: true })
  restrictLoginByEmail: boolean;

  @ApiProperty({
    description: "Habilita a autenticação de dois fatores.",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column({ name: 'enable_2fa', type: 'boolean', nullable: true })
  enable2fa: boolean;

  @ApiProperty({
    description: "Permite apenas um login ativo por usuário.",
    type: "boolean",
    example: true,
    required: false,
  })
  @Column({ name: 'unique_login', type: 'boolean', nullable: true })
  uniqueLogin: boolean;

  @ApiProperty({
    description: "Impede que um usuário use a mesma senha que usou nos últimos 'n' dias.",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column({ name: 'enforce_password_history', type: 'boolean', nullable: true })
  enforcePasswordHistory: boolean;

  @ApiProperty({
    description: "Número de senhas anteriores que não podem ser reutilizadas.",
    type: "number",
    example: 5,
    required: false,
  })
  @Column({ name: 'password_history_limit', type: 'int', nullable: true })
  passwordHistoryLimit: number;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date | null;

  @Exclude()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date | null;
}

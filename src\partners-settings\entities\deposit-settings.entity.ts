import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity({ name: 'partner_deposit_settings', schema: 'partners' })
export class DepositSettings {
  @ApiProperty({
    description: "Identificador da configuração de depósito.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @Column({ name: 'id_partner', type: 'uuid' })
  idPartner: string;

  @ApiProperty({
    description: "<PERSON>or mínimo de depósito.",
    type: "number",
    example: 3.00,
    required: false,
  })
  @Column({ name: 'min_deposit_amount', type: 'numeric', precision: 12, scale: 2, nullable: true })
  minDepositAmount: number;

  @ApiProperty({
    description: "Valor máximo de depósito.",
    type: "number",
    example: 50000.00,
    required: false,
  })
  @Column({ name: 'max_deposit_amount', type: 'numeric', precision: 12, scale: 2, nullable: true })
  maxDepositAmount: number;

  @ApiProperty({
    description: "Limite mínimo para ativar a resposta automática.",
    type: "number",
    example: 0,
    required: false,
  })
  @Column({ name: 'auto_response_threshold', type: 'int', nullable: true })
  autoResponseThreshold: number;

  @ApiProperty({
    description: "Habilita o preenchimento automático do template.",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column({ name: 'auto_fill_template_enabled', type: 'boolean', nullable: true })
  autoFillTemplateEnabled: boolean;

  @ApiProperty({
    description: "Habilita a resposta automática.",
    type: "boolean",
    example: false,
    required: false,
  })
  @Column({ name: 'auto_response_enabled', type: 'boolean', nullable: true })
  autoResponseEnabled: boolean;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date | null;

  @Exclude()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date | null;
}

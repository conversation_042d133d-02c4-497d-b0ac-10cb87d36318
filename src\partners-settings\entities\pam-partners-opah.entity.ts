import { ApiProperty } from "@nestjs/swagger";
import { Exclude } from "class-transformer";
import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity({ name: 'pam_partners_opah', schema: 'partners' })
export class PamPartnersOpah {
  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: "Descrição do parceiro.",
    type: "string",
    example: "SupremaBet",
    required: true,
  })
  @Column({ name: 'description_partner', type: 'text' })
  descriptionPartner: string;

  @ApiProperty({
    description: "Status do parceiro.",
    type: "boolean",
    example: true,
    required: false,
  })
  @Column({ name: 'active', type: 'boolean' })
  active: boolean | null;

  @ApiProperty({
    description: "Caixa do parceiro.",
    type: "string",
    example: "<EMAIL>",
    required: false,
  })
  @Column({ name: 'partner_cashier', type: 'text', nullable: true })
  partnerCashier: string | null;

  @ApiProperty({
    description: "URL de fechamento do parceiro.",
    type: "string",
    example: "https://bet.com/close",
    required: false,
  })
  @Column({ name: 'partner_close_url', type: 'text', nullable: true })
  partnerCloseUrl: string | null;

  @Exclude()
  @Column({ name: 'basic_auth', type: 'text' })
  basicAuth: string;

  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "number",
    example: 1,
    required: true,
  })
  @Column({ name: 'id_partner', type: 'int' })
  idPartner: number;

  @ApiProperty({
    description: "Email do parceiro.",
    type: "string",
    example: "<EMAIL>",
    required: false,
  })
  @Column({ name: 'email', type: 'text', nullable: true })
  email: string;

  @ApiProperty({
    description: "Telefone do parceiro.",
    type: "string",
    example: "219999-9999",
    required: false,
  })
  @Column({ name: 'phone', type: 'text', nullable: true })
  phone: string;

  @ApiProperty({
    description: "Endereço do parceiro.",
    type: "string",
    example: "Rua da cidade, 18",
    required: false,
  })
  @Column({ name: 'address', type: 'text', nullable: true })
  address: string;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @Exclude()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz', nullable: true })
  deletedAt: Date | null;
}
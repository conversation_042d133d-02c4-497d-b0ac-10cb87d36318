import { ApiProperty } from "@nestjs/swagger";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity({ name: 'partner_historic', schema: 'partners' })
export class PartnerHistoric {
    @ApiProperty({
        description: "Identificador do histórico.",
        type: "string",
        example: "123e4567-e89b-12d3-a456-************",
        required: true,
    })
    @PrimaryGeneratedColumn('uuid')
    public id: string;

    @ApiProperty({
        description: "Item antigo.",
        type: "any",
        example: {},
        required: false,
    })
    @Column({ name: 'payload_old', type: 'jsonb', nullable: true })
    public payloadOld: any

    @ApiProperty({
        description: "Item novo.",
        type: "any",
        example: {},
        required: true,
    })
    @Column({ name: 'payload_new', type: 'jsonb' })
    public payloadNew: any

    @ApiProperty({
        description: "Identificador do parceiro.",
        type: "string",
        example: "123e4567-e89b-12d3-a456-************",
        required: false,
    })
    @Column({ name: 'partner_id', type: 'uuid', nullable: true })
    public partnerId: string;

    @ApiProperty({
        description: "Identificador do usuário que criou o histórico.",
        type: "string",
        example: "123e4567-e89b-12d3-a456-************",
        required: false,
    })
    @Column({ name: 'created_by_id', type: 'uuid', nullable: true })
    public createdById: string;

    @ApiProperty({
        description: "Email do usuário que criou o histórico.",
        type: "string",
        example: "<EMAIL>",
        required: false,
    })
    @Column({ name: 'created_by', type: 'text', nullable: true })
    public createdBy: string;

    @ApiProperty({
        description: "Categoria do histórico.",
        type: "string",
        example: "general",
        required: false,
    })
    @Column({ name: 'category', type: 'text', nullable: true })
    public category: string;

    @ApiProperty({
        description: "Data de criação",
        type: "string",
        format: "date-time",
        example: "2023-10-01T00:00:00Z",
        required: true,
    })
    @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
    public createdAt: Date;
}
import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity({ name: 'partner_security_settings', schema: 'partners' })
export class SecuritySettings {
  @ApiProperty({
    description: "Identificador da configuração de segurança.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @Column({ name: 'id_partner', type: 'uuid' })
  idPartner: string;

  @ApiProperty({
    description: "Número de dias para expirar a senha.",
    type: "number",
    example: 90,
    required: false,
  })
  @Column({ name: 'password_expiration_days', type: 'int', nullable: true })
  passwordExpirationDays: number;

  @ApiProperty({
    description: "Número de dias para expirar a senha temporária.",
    type: "number",
    example: 7,
    required: false,
  })
  @Column({
    name: 'temporary_password_expiration_days',
    type: 'int',
    nullable: true,
  })
  temporaryPasswordExpirationDays: number;

  @ApiProperty({
    description: "Tamanho mínimo da senha.",
    type: "number",
    example: 8,
    required: false,
  })
  @Column({ name: 'min_password_length', type: 'int', nullable: true })
  minPasswordLength: number;

  @ApiProperty({
    description: "Expressão regular para validar a complexidade da senha.",
    type: "string",
    example: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$',
    required: false,
  })
  @Column({ name: 'password_regex', type: 'text', nullable: true })
  passwordRegex: string;

  @ApiProperty({
    description: "Número máximo de tentativas de senha antes de bloquear o acesso.",
    type: "number",
    example: 5,
    required: false,
  })
  @Column({ name: 'password_attempt_limit', type: 'int', nullable: true })
  passwordAttemptLimit: number;

  @ApiProperty({
    description: "Número de dias para notificar o usuário sobre a expiração da senha.",
    type: "number",
    example: 5,
    required: false,
  })
  @Column({
    name: 'password_expiration_notice_days',
    type: 'int',
    nullable: true,
  })
  passwordExpirationNoticeDays: number;

  @ApiProperty({
    description: "Número de minutos que o acesso será bloqueado após atingir o limite de tentativas de senha.",
    type: "number",
    example: 10,
    required: false,
  })
  @Column({ name: 'login_block_minutes', type: 'int', nullable: true })
  loginBlockMinutes: number;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date | null;

  @Exclude()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date | null;
}

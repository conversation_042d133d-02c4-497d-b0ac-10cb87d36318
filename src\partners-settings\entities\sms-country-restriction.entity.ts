import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  JoinColumn,
  DeleteDateColumn
} from 'typeorm';
import { SmsSettings } from './sms-setting.entity';
import { Region } from '@/region/entities/region.entity';

@Entity({ schema: 'partners', name: 'partner_sms_country_restrictions' })
@Unique(['idSmsSetting', 'idRegion'])
export class SmsCountryRestriction {
  @ApiProperty({
    description: "Identificador da restrição de país.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: "Identificador da configuração de SMS.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @Column({ name: 'id_sms_setting', type: 'uuid' })
  idSmsSetting: string;

  @ApiProperty({
    description: "Identificador da região.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @Column({ name: 'id_region', type: 'uuid' })
  idRegion: string;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Exclude()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date | null;

  @ApiProperty({
    description: "Configuração de SMS",
    type: "object",
    required: true,
  })
  @ManyToOne(() => SmsSettings, (setting) => setting.countryRestrictions)
  @JoinColumn({ name: 'id_sms_setting' })
  smsSetting: SmsSettings;

  @ApiProperty({
    description: "Região",
    type: "object",
    required: true,
  })
  @ManyToOne(() => Region)
  @JoinColumn({ name: 'id_region' })
  region: Region;
}

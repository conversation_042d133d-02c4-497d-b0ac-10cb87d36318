import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity({ name: 'partner_sms_provider_settings', schema: 'partners' })
export class SmsProviderSettings {
  @ApiProperty({
    description: "Identificador da configuração de provedor de SMS.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @Column({ name: 'id_partner', type: 'uuid' })
  idPartner: string;

  @ApiProperty({
    description: "Nome do provedor de SMS.",
    type: "string",
    example: "Twilio",
    required: false,
  })
  @Column({ name: 'provider_name', type: 'varchar', length: 100, nullable: true })
  providerName: string;

  @ApiProperty({
    description: "URL do provedor de SMS.",
    type: "string",
    example: "https://api.twilio.com/",
    required: false,
  })
  @Column({ name: 'provider_url', type: 'varchar', length: 255, nullable: true })
  providerUrl: string;

  @ApiProperty({
    description: "URL de verificação de status do provedor de SMS.",
    type: "string",
    example: "https://api.twilio.com/status",
    required: false,
  })
  @Column({ name: 'status_check_url', type: 'varchar', length: 255, nullable: true })
  statusCheckUrl: string;

  @ApiProperty({
    description: "URL de verificação de saldo do provedor de SMS.",
    type: "string",
    example: "https://api.twilio.com/balance",
    required: false,
  })
  @Column({ name: 'balance_check_url', type: 'varchar', length: 255, nullable: true })
  balanceCheckUrl: string;

  @ApiProperty({
    description: "Nome de usuário para autenticação no provedor de SMS.",
    type: "string",
    example: "user-123",
    required: false,
  })
  @Column({ name: 'username', type: 'varchar', length: 255, nullable: true })
  username: string;

  @ApiProperty({
    description: "Senha para autenticação no provedor de SMS.",
    type: "string",
    example: "secret-password",
    required: false,
  })
  @Column({ name: 'password', type: 'varchar', length: 255, nullable: true })
  password: string;

  @ApiProperty({
    description: "Chave de API para envio de SMS.",
    type: "string",
    example: "key-abc123",
    required: false,
  })
  @Column({ name: 'sms_api_key', type: 'varchar', length: 255, nullable: true })
  smsApiKey: string;

  @ApiProperty({
    description: "Segredo da chave de API para envio de SMS.",
    type: "string",
    example: "secret-xyz",
    required: false,
  })
  @Column({ name: 'sms_api_secret', type: 'varchar', length: 255, nullable: true })
  smsApiSecret: string;

  @ApiProperty({
    description: "Token de acesso para envio de SMS.",
    type: "string",
    example: "token-001",
    required: false,
  })
  @Column({ name: 'sms_token', type: 'varchar', length: 255, nullable: true })
  smsToken: string;

  @ApiProperty({
    description: "Número de origem para envio de SMS.",
    type: "string",
    example: "+17172826936",
    required: false,
  })
  @Column({ name: 'from_number', type: 'varchar', length: 20, nullable: true })
  fromNumber: string;

  @ApiProperty({
    description: "Tempo de espera em segundos para envio de SMS.",
    type: "number",
    example: 10,
    required: false,
  })
  @Column({ name: 'timeout_seconds', type: 'int', nullable: true })
  timeoutSeconds: number;

  @ApiProperty({
    description: "Rota para envio de SMS.",
    type: "string",
    example: "/sms/send",
    required: false,
  })
  @Column({ name: 'route', type: 'varchar', length: 255, nullable: true })
  route: string;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Exclude()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date | null;
}

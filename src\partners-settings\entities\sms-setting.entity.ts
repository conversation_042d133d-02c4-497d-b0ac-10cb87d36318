import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Unique,
  DeleteDateColumn,
} from 'typeorm';
import { SmsCountryRestriction } from './sms-country-restriction.entity';

@Entity({ schema: 'partners', name: 'partner_sms_settings' })
@Unique(['idPartner'])
export class SmsSettings {
  @ApiProperty({
    description: "Identificador da configuração de SMS.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @Column({ name: 'id_partner', type: 'uuid' })
  idPartner: string;

  @ApiProperty({
    description: "Tempo de expiração do código de SMS em segundos.",
    type: "number",
    example: 300,
    required: true,
  })
  @Column({ name: 'code_expiration_seconds', type: 'int' })
  codeExpirationSeconds: number;

  @ApiProperty({
    description: "Tamanho do código de SMS.",
    type: "number",
    example: 4,
    required: true,
  })
  @Column({ name: 'code_length', type: 'int' })
  codeLength: number;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date | null;

  @Exclude()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date | null;

  @ApiProperty({
    description: "Restrições de país para envio de SMS.",
    type: "array",
    required: false,
  })
  @OneToMany(() => SmsCountryRestriction, (restriction) => restriction.smsSetting)
  countryRestrictions: SmsCountryRestriction[];
}

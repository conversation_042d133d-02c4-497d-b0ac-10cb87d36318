import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

@Entity({ name: 'partner_withdrawal_settings', schema: 'partners' })
export class WithdrawalSettings {
  @ApiProperty({
    description: "Identificador da configuração de retirada.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: "Identificador do parceiro.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @Column({ name: 'id_partner', type: 'uuid' })
  idPartner: string;

  @ApiProperty({
    description: "Número máximo de solicitações de retirada por dia.",
    type: "number",
    example: 5,
    required: false,
  })
  @Column({ name: 'max_requests_per_day', type: 'int', nullable: true })
  maxRequestsPerDay: number;

  @ApiProperty({
    description: "Valor mínimo de retirada.",
    type: "number",
    example: 10.0,
    required: false,
  })
  @Column({ name: 'min_withdrawal_amount', type: 'numeric', precision: 12, scale: 2, nullable: true })
  minWithdrawalAmount: number;

  @ApiProperty({
    description: "Valor máximo de retirada.",
    type: "number",
    example: 50000.0,
    required: false,
  })
  @Column({ name: 'max_withdrawal_amount', type: 'numeric', precision: 12, scale: 2, nullable: true })
  maxWithdrawalAmount: number;

  @ApiProperty({
    description: "Número máximo de solicitações pendentes.",
    type: "number",
    example: 2,
    required: false,
  })
  @Column({ name: 'max_pending_requests', type: 'int', nullable: true })
  maxPendingRequests: number;

  @ApiProperty({
    description: "Valor máximo de retirada paga por dia.",
    type: "number",
    example: 250000.0,
    required: false,
  })
  @Column({ name: 'max_paid_amount_per_day', type: 'numeric', precision: 12, scale: 2, nullable: true })
  maxPaidAmountPerDay: number;

  @ApiProperty({
    description: "Número máximo de solicitações pagas por dia.",
    type: "number",
    example: 5,
    required: false,
  })
  @Column({ name: 'max_paid_requests_per_day', type: 'int', nullable: true })
  maxPaidRequestsPerDay: number;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date | null;

  @Exclude()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date | null;
}

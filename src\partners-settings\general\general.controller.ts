import {
  Controller,
  Get,
  Inject,
  Body,
  Patch,
  UseGuards,
  Post,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { PartnerId } from '@/common/decorators/partner-id.decorator';
import { PartnerSettingsGeneralService } from './general.service';
import { PutGeneralSettingsPayloadDto, PutGeneralSettingsResponseDto } from '../dto/put-general-settings.dto';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { BackofficeUser } from '@/common/decorators/backoffice-user.decorator';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { CreateGeneralSettingsPayloadDto } from '../dto/post-general-settings.dto';
import { PamPartnersOpah } from '../entities/pam-partners-opah.entity';

@Controller('backoffice/partners/general')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@ApiTags('Settings Partners General')
export class PartnerSettingsGeneralController {
  @Inject() private readonly generalService: PartnerSettingsGeneralService;

  @Get()
  @ApiOperation({ summary: 'Busca as configurações gerais do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiOkResponse({ type: PamPartnersOpah, isArray: false })
  async findAll(@PartnerId() partnerId: string) {
    return await this.generalService.findOne(partnerId);
  }

  @Post()
  @ApiOperation({ summary: 'Cria configurações gerais para o parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateGeneralSettingsPayloadDto })
  @ApiOkResponse({ type: PutGeneralSettingsResponseDto })
  async create(
    @PartnerId() partnerId: string,
    @Body() data: CreateGeneralSettingsPayloadDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    return await this.generalService.create(partnerId, data, backofficeUser);
  }
  @Patch()
  @ApiOperation({ summary: 'Atualiza configurações gerais do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: PutGeneralSettingsPayloadDto })
  @ApiOkResponse({ type: PutGeneralSettingsResponseDto })
  async update(
    @PartnerId() partnerId: string,
    @Body() data: PutGeneralSettingsPayloadDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    return await this.generalService.update(partnerId, data, backofficeUser);
  }
}

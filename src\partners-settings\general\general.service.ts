import { Injectable, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { PamPartnersOpah } from '../entities/pam-partners-opah.entity';
import { PutGeneralSettingsPayloadDto, PutGeneralSettingsResponseDto } from '../dto/put-general-settings.dto';
import { PartnerHistoric } from '../entities/partner-historic.entity';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { CreateGeneralSettingsPayloadDto } from '../dto/post-general-settings.dto';

@Injectable()
export class PartnerSettingsGeneralService {
  constructor(
    @InjectRepository(PamPartnersOpah, 'partners')
    private readonly generalSettingsRepository: Repository<PamPartnersOpah>,

    @InjectDataSource('partners')
    private readonly dataSource: DataSource,
  ) {}

  async findOne(partnerId: string): Promise<PamPartnersOpah> {
    return this.generalSettingsRepository.findOne({
      where: { id: partnerId },
    });
  }


  async create(partnerId: string, body: CreateGeneralSettingsPayloadDto, backofficeUser: IBackofficeUser): Promise<PutGeneralSettingsResponseDto> {
    const exists = await this.generalSettingsRepository.findOne({
      where: { id: partnerId },
    });

    if (exists) {
      throw new ConflictException('Configurações Gerais já existem para este parceiro.');
    }

    return this.dataSource.transaction(async (manager) => {
      const generalRepo = manager.getRepository(PamPartnersOpah);
      const historicRepo = manager.getRepository(PartnerHistoric);


      const entity = generalRepo.create({
        id: partnerId,
        ...body,
      });

      const createPartnerActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'general',
        partnerId: partnerId,
        payloadNew: entity,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId
      });

      await historicRepo.save(createPartnerActivity);

      return await generalRepo.save(entity);
    });
  }


  async update(partnerId: string, body: PutGeneralSettingsPayloadDto, backofficeUser: IBackofficeUser): Promise<PutGeneralSettingsResponseDto> {
    return this.dataSource.transaction(async (manager) => {
      const generalRepo = manager.getRepository(PamPartnersOpah);
      const historicRepo = manager.getRepository(PartnerHistoric);

      const existing = await generalRepo.findOne({
        where: { id: partnerId },
      });

      if (!existing) {
        throw new BadRequestException('Configurações Gerais não encontradas');
      }

      const updated = await generalRepo.update(existing.id, {
        descriptionPartner: body.descriptionPartner,
        address: body.address,
        phone: body.phone,
        email: body.email,
      });

      if (!updated?.affected) {
        throw new BadRequestException('Não foi possível atualizar as configurações gerais');
      }

      const createPartnerActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'general',
        partnerId: partnerId,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId,
        payloadOld: {
          descriptionPartner: existing.descriptionPartner,
          address: existing.address,
          phone: existing.phone,
          email: existing.email,
        },
        payloadNew: {
          descriptionPartner: body.descriptionPartner,
          address: body.address,
          phone: body.phone,
          email: body.email,
        },
      });

      await historicRepo.save(createPartnerActivity);

      const result = await generalRepo.findOne({
        where: { id: partnerId },
      });

      return {
        descriptionPartner: result.descriptionPartner,
        address: result.address,
        phone: result.phone,
        email: result.email,
        id: result.id,
      };
    });
  }
}

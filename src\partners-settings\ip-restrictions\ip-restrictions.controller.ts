import {
  Controller,
  Get,
  Inject,
  Post,
  Body,
  Delete,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { IpRestrictionsService } from './ip-restrictions.service';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiHeader,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CreateIpRestrictionDto } from '../dto/create-ip-restriction.dto';
import { PartnerId } from '@/common/decorators/partner-id.decorator';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { BackofficeUser } from '@/common/decorators/backoffice-user.decorator';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { IpRestriction } from '../entities/ip-restriction.entity';

@Controller('backoffice/partners/ip-restrictions')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@ApiTags('Settings Partners Ip-Restrictions')
export class IpRestrictionsController {
  @Inject() private readonly ipRestrictionsService: IpRestrictionsService;

  @Get()
  @ApiOperation({ summary: 'Retorna todas as restrições de IP do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página atual' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: 'Quantidade de itens por página' })
  @ApiOkResponse({ type: IpRestriction, isArray: true })
  async findAll(
    @PartnerId() partnerId: string | number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10,
  ) {
    return await this.ipRestrictionsService.findAll(partnerId, {
      page,
      pageSize,
    });
  }

  @Post()
  @ApiOperation({ summary: 'Cria restrições de IP para o parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: [CreateIpRestrictionDto] })
  @ApiCreatedResponse({ type: [IpRestriction] })
  async create(
    @PartnerId() partnerId: string,
    @Body() data: CreateIpRestrictionDto[],
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    return await this.ipRestrictionsService.create(
      partnerId,
      data,
      backofficeUser,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remove restrição de IP do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiNotFoundResponse({ description: 'Restrição não encontrada' })
  @ApiNoContentResponse({ description: 'Restrição removida com sucesso' })
  async delete(
    @PartnerId() partnerId: string,
    @Param('id') ipRestrictionId: string,
  ) {
    return await this.ipRestrictionsService.delete(partnerId, ipRestrictionId);
  }
}

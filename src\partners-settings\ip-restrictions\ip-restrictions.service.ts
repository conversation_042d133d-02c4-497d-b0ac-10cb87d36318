import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { CreateIpRestrictionDto } from '../dto/create-ip-restriction.dto';
import { IpRestriction } from '../entities/ip-restriction.entity';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { PaginatedResponseDto } from '@/common/dto/paginated-resonse.dto';
import { PamPartnersOpah } from '../entities/pam-partners-opah.entity';
import { resolvePartnerUuid } from '@/common/utils/resolve-partner-uuid';
import { PartnerHistoric } from '../entities/partner-historic.entity';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class IpRestrictionsService {
  constructor(
    @InjectRepository(IpRestriction, 'partners')
    private readonly ipRestrictionRepository: Repository<IpRestriction>,

    @InjectRepository(PamPartnersOpah, 'partners')
    private readonly pamPartnersRepository: Repository<PamPartnersOpah>,

    @InjectRepository(PartnerHistoric, 'partners')
    private readonly partnerHistoricRepository: Repository<PartnerHistoric>,

    @InjectDataSource('partners')
    private readonly dataSource: DataSource,
  ) { }

  async findAll(
    partnerId: string | number,
    pagination: { page: number; pageSize: number },
  ): Promise<PaginatedResponseDto<IpRestriction>> {
    const { page, pageSize } = pagination;

    const partnerUuid = await resolvePartnerUuid(partnerId, this.pamPartnersRepository);

    const [data, total] = await this.ipRestrictionRepository.findAndCount({
      where: { idPartner: partnerUuid, deletedAt: null },
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { createdAt: 'DESC' },
    });

    return {
      data: data,
      currentPage: page,
      pageSize: pageSize,
      totalItems: total,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async create(partnerId: string, body: CreateIpRestrictionDto[], backofficeUser: IBackofficeUser): Promise<IpRestriction[]> {

    const ipAddresses = body.map((item) => item.ipAddress);

    const existing = await this.ipRestrictionRepository.find({
      where: ipAddresses.map((ip) => ({
        idPartner: partnerId,
        ipAddress: ip,
        deletedAt: null,
      })),
    });

    if (existing.length > 0) {
      const duplicates = existing.map((e) => e.ipAddress);
      throw new BadRequestException(
        `Os seguintes IPs já estão cadastrados: ${duplicates.join(', ')}`,
      );
    }
    return this.dataSource.transaction(async (manager) => {
      const ipRepo = manager.getRepository(IpRestriction);
      const historicRepo = manager.getRepository(PartnerHistoric);



      const toInsert = body.map((item) =>
        ipRepo.create({
          idPartner: partnerId,
          ipAddress: item.ipAddress,
        }),
      );

      const createActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'ip-restrictions',
        partnerId: partnerId,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId,
        payloadOld: existing.map((e) => ({
          ipAddress: e.ipAddress,
        })),
        payloadNew: toInsert.map((e) => ({
          ipAddress: e.ipAddress,
        })),
      });

      await historicRepo.save(createActivity);
      return await ipRepo.save(toInsert);
    });
  }

  async delete(partnerId: string, ipRestrictionId: string): Promise<void> {
    return this.dataSource.transaction(async (manager) => {
      const ipRepo = manager.getRepository(IpRestriction);

      const entity = await ipRepo.findOne({
        where: {
          id: ipRestrictionId,
          idPartner: partnerId,
          deletedAt: null,
        },
      });

      if (!entity || entity.deletedAt) {
        throw new NotFoundException('Restrição de IP não encontrada ou já removida.');
      }

      entity.deletedAt = new Date();
      await ipRepo.save(entity);
    });
  }
}

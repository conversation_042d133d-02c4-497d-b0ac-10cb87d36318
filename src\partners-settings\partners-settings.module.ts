import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IpRestriction } from './entities/ip-restriction.entity';
import { IpRestrictionsController } from './ip-restrictions/ip-restrictions.controller';
import { IpRestrictionsService } from './ip-restrictions/ip-restrictions.service';
import { SecuritySettingsController } from './security-settings/security-settings.controller';
import { SecuritySettingsService } from './security-settings/security-settings.service';
import { SecuritySettings } from './entities/security-settings.entity';
import { AuthSettingsService } from './auth-settings/auth-settings.service';
import { AuthSettingsController } from './auth-settings/auth-settings.controller';
import { AuthSettings } from './entities/auth-settings.entity';
import { WithdrawalSettingsService } from './withdrawal-settings/withdrawal-settings.service';
import { WithdrawalSettingsController } from './withdrawal-settings/withdrawal-settings.controller';
import { WithdrawalSettings } from './entities/withdrawal-settings.entity';
import { DepositSettingsService } from './deposit-settings/deposit-settings.service';
import { DepositSettingsController } from './deposit-settings/deposit-settings.controller';
import { DepositSettings } from './entities/deposit-settings.entity';
import { SmsProviderSettingsController } from './sms-provider-settings/sms-provider-settings.controller';
import { SmsProviderSettingsService } from './sms-provider-settings/sms-provider-settings.service';
import { SmsProviderSettings } from './entities/sms-provider-settings.entity';
import { PamPartnersOpah } from './entities/pam-partners-opah.entity';
import { PartnerSettingsGeneralController } from './general/general.controller';
import { PartnerSettingsGeneralService } from './general/general.service';
import { SmsSettingsService } from './sms-settings/sms-settings.service';
import { SmsSettingsController } from './sms-settings/sms-settings.controller';
import { SmsSettings } from './entities/sms-setting.entity';
import { SmsCountryRestriction } from './entities/sms-country-restriction.entity';
import { PartnerHistoric } from './entities/partner-historic.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      IpRestriction,
      SecuritySettings,
      AuthSettings,
      WithdrawalSettings,
      DepositSettings,
      SmsProviderSettings,
      PamPartnersOpah,
      SmsSettings,
      SmsCountryRestriction,
      PartnerHistoric
    ], 'partners'),
  ],
  controllers: [
    IpRestrictionsController,
    SecuritySettingsController,
    AuthSettingsController,
    WithdrawalSettingsController,
    DepositSettingsController,
    SmsProviderSettingsController,
    PartnerSettingsGeneralController,
    SmsSettingsController
  ],
  providers: [
    IpRestrictionsService,
    SecuritySettingsService,
    AuthSettingsService,
    WithdrawalSettingsService,
    DepositSettingsService,
    SmsProviderSettingsService,
    PartnerSettingsGeneralService,
    SmsSettingsService
  ]
})
export class PartnersSettingsModule { }
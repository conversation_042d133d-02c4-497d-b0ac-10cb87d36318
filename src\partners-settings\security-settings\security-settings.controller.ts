import {
  Controller,
  Get,
  Inject,
  Patch,
  Post,
  Body,
  UseGuards,
} from '@nestjs/common';
import {
  Api<PERSON>eader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiNotFoundResponse,
  ApiCreatedResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SecuritySettingsService } from './security-settings.service';
import { PartnerId } from '@/common/decorators/partner-id.decorator';
import { CreateOrUpdateSecuritySettingsDto } from '../dto/create-update-security-settings.dto';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { BackofficeUser } from '@/common/decorators/backoffice-user.decorator';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { SecuritySettings } from '../entities/security-settings.entity';

@Controller('backoffice/partners/security-settings')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@ApiTags('Settings Partners Security')
export class SecuritySettingsController {
  @Inject() private readonly securitySettingsService: SecuritySettingsService;

  @Get()
  @ApiOperation({ summary: 'Busca as configurações de segurança do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiOkResponse({ type: [SecuritySettings] })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async findAll(@PartnerId() partnerId: string) {
    const result = await this.securitySettingsService.findAll(partnerId);
    return result;
  }

  @Post()
  @ApiOperation({ summary: 'Cria configurações de segurança para o parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateSecuritySettingsDto })
  @ApiCreatedResponse({ type: SecuritySettings })
  @ApiConflictResponse({ description: 'Configuração já existente' })
  async create(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateSecuritySettingsDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    const result = await this.securitySettingsService.create(
      partnerId,
      body,
      backofficeUser,
    );
    return result;
  }

  @Patch()
  @ApiOperation({ summary: 'Atualiza configurações de segurança do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateSecuritySettingsDto })
  @ApiOkResponse({ type: SecuritySettings })
  @ApiBadRequestResponse({ description: 'Campos inválidos' })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async update(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateSecuritySettingsDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    console.log('body', body);
    console.log('partnerId', partnerId);
    console.log('backofficeUser', backofficeUser);
    const result = await this.securitySettingsService.update(
      partnerId,
      body,
      backofficeUser,
    );
    return result;
  }
}

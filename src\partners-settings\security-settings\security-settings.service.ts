import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { SecuritySettings } from '../entities/security-settings.entity';
import { CreateOrUpdateSecuritySettingsDto } from '../dto/create-update-security-settings.dto';
import { PartnerHistoric } from '../entities/partner-historic.entity';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';

@Injectable()
export class SecuritySettingsService {
  constructor(
    @InjectRepository(SecuritySettings, 'partners')
    private readonly repository: Repository<SecuritySettings>,
    @InjectDataSource('partners')
    private readonly dataSource: DataSource,
  ) {}

  async findAll(partnerId: string): Promise<SecuritySettings[]> {
    const result = await this.repository.find({
      where: { idPartner: partnerId, deletedAt: null },
    });

    return result;
  }

  async create(
    partnerId: string,
    dto: CreateOrUpdateSecuritySettingsDto,
    backofficeUser: IBackofficeUser,
  ): Promise<SecuritySettings> {
    const exists = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (exists) {
      throw new ConflictException(
        'Configurações de segurança já existem para este parceiro.',
      );
    }
    return this.dataSource.transaction(async (manager) => {
      const repo = manager.getRepository(SecuritySettings);
      const historicRepo = manager.getRepository(PartnerHistoric);

      const entity = repo.create({
        idPartner: partnerId,
        ...dto,
      });

      const createPartnerActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'security',
        partnerId: partnerId,
        payloadNew: dto,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId,
      });

      await historicRepo.save(createPartnerActivity);
      return await repo.save(entity);
    });
  }

  async update(
    partnerId: string,
    dto: CreateOrUpdateSecuritySettingsDto,
    backofficeUser: IBackofficeUser,
  ): Promise<SecuritySettings> {
    console.log('dto', dto);
    console.log('partnerId', partnerId);
    console.log('backofficeUser', backofficeUser);

    const existing = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (!existing) {
      throw new NotFoundException(
        'Configurações de segurança não encontradas.',
      );
    }

    console.log('existing', existing);
    return this.dataSource.transaction(async (manager) => {
      const repo = manager.getRepository(SecuritySettings);
      const historicRepo = manager.getRepository(PartnerHistoric);

      const createPartnerActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'general',
        partnerId: partnerId,
        payloadOld: existing,
        payloadNew: dto,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId,
      });

      Object.assign(existing, {
        ...dto,
        updated_at: new Date(),
      });

      await historicRepo.save(createPartnerActivity);
      return await repo.save(existing);
    });
  }
}

import {
  Controller,
  Get,
  Post,
  Patch,
  Inject,
  Body,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiHeader,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiConflictResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { PartnerId } from '@/common/decorators/partner-id.decorator';
import { SmsProviderSettingsService } from './sms-provider-settings.service';
import { CreateOrUpdateSmsProviderDto } from '../dto/create-update-sms-provider.dto';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { BackofficeUser } from '@/common/decorators/backoffice-user.decorator';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { SmsProviderSettings } from '../entities/sms-provider-settings.entity';

@Controller('backoffice/partners/sms-provider-settings')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@ApiTags('Settings Partners SMS Provider')
export class SmsProviderSettingsController {
  @Inject()
  private readonly smsProviderSettingsService: SmsProviderSettingsService;

  @Get()
  @ApiOperation({ summary: 'Busca configurações do provedor SMS' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiOkResponse({ type: [SmsProviderSettings] })
  @ApiNotFoundResponse({ description: 'Configuração não encontrada' })
  async findAll(@PartnerId() partnerId: string) {
    const result = await this.smsProviderSettingsService.findAll(partnerId);

    return result;
  }

  @Post()
  @ApiOperation({ summary: 'Cria configurações do provedor SMS' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateSmsProviderDto })
  @ApiCreatedResponse({ type: SmsProviderSettings })
  @ApiConflictResponse({ description: 'Já existe uma configuração' })
  async create(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateSmsProviderDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    const result = await this.smsProviderSettingsService.create(
      partnerId,
      body,
      backofficeUser,
    );
    const { password, smsApiSecret: sms_api_secret, ...safe } = result;

    return {
      ...safe,
      created_at: safe.createdAt.toISOString(),
      updated_at: safe.updatedAt?.toISOString() ?? null,
    };
  }

  @Patch()
  @ApiOperation({ summary: 'Atualiza configurações do provedor SMS' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateSmsProviderDto })
  @ApiOkResponse({ type: SmsProviderSettings })
  @ApiBadRequestResponse({ description: 'Campos inválidos' })
  @ApiNotFoundResponse({ description: 'Configuração não encontrada' })
  async update(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateSmsProviderDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    const result = await this.smsProviderSettingsService.update(
      partnerId,
      body,
      backofficeUser,
    );
    const { password, smsApiSecret: sms_api_secret, ...safe } = result;

    return {
      ...safe,
      created_at: safe.createdAt.toISOString(),
      updated_at: safe.updatedAt?.toISOString() ?? null,
    };
  }
}

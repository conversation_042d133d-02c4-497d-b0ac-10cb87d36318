import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { SmsProviderSettings } from '../entities/sms-provider-settings.entity';
import { CreateOrUpdateSmsProviderDto } from '../dto/create-update-sms-provider.dto';
import { PartnerHistoric } from '../entities/partner-historic.entity';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';

@Injectable()
export class SmsProviderSettingsService {
  constructor(
    @InjectRepository(SmsProviderSettings, 'partners')
    private readonly repository: Repository<SmsProviderSettings>,

    @InjectDataSource('partners')
    private readonly dataSource: DataSource,
  ) {}

  async findAll(partnerId: string): Promise<SmsProviderSettings[]> {
    const result = await this.repository.find({
      where: { idPartner: partnerId, deletedAt: null },
    });

    return result;
  }

  async create(partnerId: string, dto: CreateOrUpdateSmsProviderDto, backofficeUser:IBackofficeUser): Promise<SmsProviderSettings> {
    const exists = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (exists) {
      throw new ConflictException('Já existe uma configuração cadastrada para este parceiro.');
    }


    return this.dataSource.transaction(async (manager) => {
      const repo = manager.getRepository(SmsProviderSettings);
      const historicRepo = manager.getRepository(PartnerHistoric);
  
      const entity = repo.create({
        idPartner: partnerId,
        ...dto,
      });

      const createPartnerActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'sms-provider',
        partnerId: partnerId,
        payloadNew: entity,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId
      });

      await historicRepo.save(createPartnerActivity);
      return await repo.save(entity);
    });
  }

  async update(partnerId: string, dto: CreateOrUpdateSmsProviderDto, backofficeUser:IBackofficeUser): Promise<SmsProviderSettings> {
    const existing = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (!existing) {
      throw new NotFoundException('Configurações de provedor SMS não encontradas.');
    }

    return this.dataSource.transaction(async (manager) => {
      const repo = manager.getRepository(SmsProviderSettings);
      const historicRepo = manager.getRepository(PartnerHistoric);

      const createPartnerActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'sms-provider',
        partnerId: partnerId,
        payloadOld: existing,
        payloadNew: dto,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId
      });

      Object.assign(existing, {
        ...dto,
        updated_at: new Date(),
      });

      await historicRepo.save(createPartnerActivity);
      return await repo.save(existing);
    });
  }
}

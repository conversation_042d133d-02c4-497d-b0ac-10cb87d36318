import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { SmsSettingsService } from './sms-settings.service';
import { PartnerId } from '@/common/decorators/partner-id.decorator';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConflictResponse,
  ApiHeader,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { SmsSettingResponseDto } from '../dto/sms-settings-response.dto';
import { CreateOrUpdateSmsSettingDto } from '../dto/create-update-sms-settings.dto';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { BackofficeUser } from '@/common/decorators/backoffice-user.decorator';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';

@Controller('backoffice/partners/sms-settings')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@ApiTags('Settings Partners SMS')
export class SmsSettingsController {
  @Inject() private readonly smsSettingsService: SmsSettingsService;

  @Get()
  @ApiOperation({ summary: 'Busca as configurações de SMS do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiResponse({ type: SmsSettingResponseDto })
  async find(@PartnerId() partnerId: string) {
    const result = await this.smsSettingsService.find(partnerId);

    return result;
  }

  @Post()
  @ApiOperation({ summary: 'Cria configurações de sms para o parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateSmsSettingDto })
  @ApiResponse({ type: SmsSettingResponseDto })
  @ApiConflictResponse({ description: 'Configuração já existente' })
  async create(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateSmsSettingDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    return await this.smsSettingsService.create(
      partnerId,
      body,
      backofficeUser,
    );
  }

  @Patch()
  @ApiOperation({ summary: 'Atualiza configurações de sms do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateSmsSettingDto })
  @ApiOkResponse({ type: SmsSettingResponseDto })
  @ApiBadRequestResponse({ description: 'Campos inválidos' })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async update(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateSmsSettingDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    const result = await this.smsSettingsService.update(
      partnerId,
      body,
      backofficeUser,
    );

    return result;
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remove configurações de sms do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiNotFoundResponse({ description: 'Restrição não encontrada' })
  @ApiNoContentResponse({ description: 'Restrição removida com sucesso' })
  async delete(
    @PartnerId() partnerId: string,
    @Param('id') ipRestrictionId: string,
  ) {
    return await this.smsSettingsService.delete(partnerId, ipRestrictionId);
  }
}

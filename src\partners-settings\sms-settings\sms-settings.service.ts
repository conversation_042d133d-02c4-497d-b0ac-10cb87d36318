import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { SmsSettings } from '../entities/sms-setting.entity';
import { CreateOrUpdateSmsSettingDto } from '../dto/create-update-sms-settings.dto';
import { SmsCountryRestriction } from '../entities/sms-country-restriction.entity';
import { PaginatedResponseDto } from '@/common/dto/paginated-resonse.dto';
import { SmsSettingResponseDto } from '../dto/sms-settings-response.dto';
import { SmsCountryRestrictionResponseDto } from '../dto/sms-country-restriction-response.dto';
import { PartnerHistoric } from '../entities/partner-historic.entity';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class SmsSettingsService {
  constructor(
    @InjectRepository(SmsSettings, 'partners')
    private readonly smsSettingRepository: Repository<SmsSettings>,

    @InjectRepository(SmsCountryRestriction, 'partners')
    private readonly smsCountryRepository: Repository<SmsCountryRestriction>,

    @InjectDataSource('partners')
    private readonly dataSource: DataSource,
  ) { }

  async find(partnerId: string): Promise<SmsSettingResponseDto> {
    const setting = await this.smsSettingRepository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (!setting) {
      return {
        id: null,
        idPartner: null,
        codeExpirationSeconds: null,
        codeLength: null,
        createdAt: null,
        updatedAt: null,
        countryRestrictions: [],
      };
    }

    const restrictions = await this.smsCountryRepository
      .createQueryBuilder('restriction')
      .leftJoinAndSelect('restriction.region', 'region')
      .where('restriction.smsSetting = :settingId', { settingId: setting.id })
      .andWhere('restriction.deletedAt IS NULL')
      .andWhere('region.deletedAt IS NULL')
      .orderBy('restriction.createdAt', 'DESC')
      .getMany();

    const mappedRestrictions: SmsCountryRestrictionResponseDto[] = restrictions.map((r) => ({
      id: r.id,
      idSmsSetting: r.idSmsSetting,
      idRegion: r.idRegion,
      regionName: r.region.name,
      createdAt: r.createdAt,
      updatedAt: r.updatedAt ?? null,
    }));

    const settingResponse = plainToInstance(SmsSettingResponseDto, setting);

    return {
      id: settingResponse.id,
      idPartner: settingResponse.idPartner,
      codeExpirationSeconds: settingResponse.codeExpirationSeconds,
      codeLength: settingResponse.codeLength,
      createdAt: settingResponse.createdAt,
      updatedAt: settingResponse.updatedAt ?? null,
      countryRestrictions: plainToInstance(SmsCountryRestrictionResponseDto, mappedRestrictions),
    };
  }

  async create(partnerId: string, dto: CreateOrUpdateSmsSettingDto, backofficeUser: IBackofficeUser): Promise<SmsSettingResponseDto> {
    const exists = await this.smsSettingRepository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (exists) {
      throw new ConflictException('Configuração de SMS já existe para este parceiro.');
    }

    return this.dataSource.transaction(async (manager) => {
      const smsSettingRepo = manager.getRepository(SmsSettings);
      const smsCountryRepo = manager.getRepository(SmsCountryRestriction);
      const historicRepo = manager.getRepository(PartnerHistoric);


      const setting = smsSettingRepo.create({
        idPartner: partnerId,
        codeExpirationSeconds: dto.codeExpirationSeconds,
        codeLength: dto.codeLength,
      });

      const savedSetting = await smsSettingRepo.save(setting);

      let restrictions = [];
      if (dto.regionIds?.length) {
        restrictions = dto.regionIds.map((regionId) =>
          smsCountryRepo.create({
            idSmsSetting: savedSetting.id,
            idRegion: regionId,
          }),
        );
      }

      const createPartnerActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'sms-provider',
        partnerId: partnerId,
        payloadNew: restrictions,
        createdBy: backofficeUser.email,
        createdById: backofficeUser.userId
      });

      await historicRepo.save(createPartnerActivity);

      if (restrictions.length > 0) {
        await smsCountryRepo.save(restrictions);
      }

      return this.find(partnerId);
    });
  }

  async update(partnerId: string, dto: CreateOrUpdateSmsSettingDto, backofficeUser: IBackofficeUser): Promise<SmsSettingResponseDto> {

    const setting = await this.smsSettingRepository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (!setting) {
      throw new NotFoundException('Configuração de SMS não encontrada.');
    }

    return this.dataSource.transaction(async (manager) => {
      const smsSettingRepo = manager.getRepository(SmsSettings);
      const smsCountryRepo = manager.getRepository(SmsCountryRestriction);
      const historicRepo = manager.getRepository(PartnerHistoric);


      Object.assign(setting, {
        codeExpirationSeconds: dto.codeExpirationSeconds,
        codeLength: dto.codeLength,
        updatedAt: new Date(),
      });

      await smsSettingRepo.save(setting);

      if (dto.regionIds && dto.regionIds.length > 0) {
        const existing = await smsCountryRepo.find({
          where: {
            idSmsSetting: setting.id,
            deletedAt: null,
          },
        });

        const existingIds = existing.map((e) => e.idRegion);
        const toInsert = dto.regionIds.filter((id) => !existingIds.includes(id));

        const createPartnerActivity = historicRepo.create({
          createdAt: new Date(),
          category: 'sms-provider',
          partnerId: partnerId,
          payloadOld: existing,
          payloadNew: dto,
          createdBy: backofficeUser.email,
          createdById: backofficeUser.userId
        });

        await historicRepo.save(createPartnerActivity);

        if (toInsert.length > 0) {
          const newRestrictions = toInsert.map((regionId) =>
            smsCountryRepo.create({
              idSmsSetting: setting.id,
              idRegion: regionId,
            }),
          );

          await smsCountryRepo.save(newRestrictions);
        }
      }

      return this.find(partnerId);
    });
  }

  async delete(partnerId: string, ipRestrictionId: string): Promise<void> {
    const entity = await this.smsCountryRepository.findOne({
      where: {
        id: ipRestrictionId,
        deletedAt: null,
      },
    });

    if (!entity || entity.deletedAt) {
      throw new NotFoundException('Configuração de SMS não encontrada ou já removida.');
    }

    await this.smsCountryRepository.update({ id: ipRestrictionId }, { deletedAt: new Date() });
  }
}

import {
  Controller,
  Get,
  Inject,
  Post,
  Patch,
  Body,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiHeader,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiConflictResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { WithdrawalSettingsService } from './withdrawal-settings.service';
import { CreateOrUpdateWithdrawalSettingsDto } from '../dto/create-update-withdrawal-settings.dto';
import { PartnerId } from '@/common/decorators/partner-id.decorator';
import { BackofficeGuard } from '@/common/guard/backoffice/backoffice.guard';
import { BackofficeUser } from '@/common/decorators/backoffice-user.decorator';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';
import { WithdrawalSettings } from '../entities/withdrawal-settings.entity';

@Controller('backoffice/partners/withdrawal-settings')
@UseGuards(BackofficeGuard)
@ApiBearerAuth('access-token')
@ApiTags('Settings Partners Withdrawal')
export class WithdrawalSettingsController {
  @Inject()
  private readonly withdrawalSettingsService: WithdrawalSettingsService;

  @Get()
  @ApiOperation({ summary: 'Busca as configurações de retirada do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiOkResponse({ type: [WithdrawalSettings] })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async findAll(@PartnerId() partnerId: string) {
    const result = await this.withdrawalSettingsService.findAll(partnerId);

    return result;
  }

  @Post()
  @ApiOperation({ summary: 'Cria configurações de retirada para o parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateWithdrawalSettingsDto })
  @ApiCreatedResponse({ type: WithdrawalSettings })
  @ApiConflictResponse({ description: 'Configurações já existentes' })
  async create(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateWithdrawalSettingsDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    const result = await this.withdrawalSettingsService.create(
      partnerId,
      body,
      backofficeUser,
    );
    return {
      ...result,
      created_at: result.createdAt.toISOString(),
      updated_at: result.updatedAt?.toISOString() ?? null,
    };
  }

  @Patch()
  @ApiOperation({ summary: 'Atualiza configurações de retirada do parceiro' })
  @ApiHeader({ name: 'partner-id', required: true, description: 'Identificador do parceiro' })
  @ApiBody({ type: CreateOrUpdateWithdrawalSettingsDto })
  @ApiOkResponse({ type: WithdrawalSettings })
  @ApiBadRequestResponse({ description: 'Campos inválidos' })
  @ApiNotFoundResponse({ description: 'Configurações não encontradas' })
  async update(
    @PartnerId() partnerId: string,
    @Body() body: CreateOrUpdateWithdrawalSettingsDto,
    @BackofficeUser() backofficeUser: IBackofficeUser,
  ) {
    const result = await this.withdrawalSettingsService.update(
      partnerId,
      body,
      backofficeUser,
    );
    return {
      ...result,
      created_at: result.createdAt.toISOString(),
      updated_at: result.updatedAt?.toISOString() ?? null,
    };
  }
}

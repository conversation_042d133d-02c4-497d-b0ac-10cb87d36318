import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { WithdrawalSettings } from '../entities/withdrawal-settings.entity';
import { CreateOrUpdateWithdrawalSettingsDto } from '../dto/create-update-withdrawal-settings.dto';
import { PartnerHistoric } from '../entities/partner-historic.entity';
import { IBackofficeUser } from '@/common/interfaces/backoffice-user.interface';

@Injectable()
export class WithdrawalSettingsService {
  constructor(
    @InjectRepository(WithdrawalSettings, 'partners')
    private readonly repository: Repository<WithdrawalSettings>,
    @InjectRepository(PartnerHistoric, 'partners')
    private readonly partnerHistoricRepository: Repository<PartnerHistoric>,
    @InjectDataSource('partners')
    private readonly dataSource: DataSource,
  ) {}

  async findAll(partnerId: string): Promise<WithdrawalSettings[]> {
    const result = await this.repository.find({
      where: { idPartner: partnerId, deletedAt: null },
    });

    return result;
  }

  async create(
    partnerId: string,
    dto: CreateOrUpdateWithdrawalSettingsDto,
    backofficeUser: IBackofficeUser,
  ): Promise<WithdrawalSettings> {
    const exists = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (exists) {
      throw new ConflictException(
        'Configurações já existem para este parceiro.',
      );
    }

    const entity = this.repository.create({
      idPartner: partnerId,
      ...dto,
    });

    const createPartnerActivity = this.partnerHistoricRepository.create({
      createdAt: new Date(),
      category: 'withdrawal',
      partnerId: partnerId,
      payloadNew: entity,
      createdBy: backofficeUser.email,
      createdById: backofficeUser.userId,
    });

    await this.partnerHistoricRepository.save(createPartnerActivity);

    return await this.repository.save(entity);
  }

  async update(
    partnerId: string,
    dto: CreateOrUpdateWithdrawalSettingsDto,
    user: IBackofficeUser,
  ): Promise<WithdrawalSettings> {
    const existing = await this.repository.findOne({
      where: { idPartner: partnerId, deletedAt: null },
    });

    if (!existing) {
      throw new NotFoundException('Configurações de retirada não encontradas.');
    }

    const oldPayload = existing;
    const newPayload = { ...existing, ...dto };

    return await this.dataSource.transaction(async (manager) => {
      const historicRepo = manager.getRepository(PartnerHistoric);
      const settingsRepo = manager.getRepository(WithdrawalSettings);

      const createPartnerActivity = historicRepo.create({
        createdAt: new Date(),
        category: 'withdrawal',
        partnerId: partnerId,
        payloadOld: oldPayload,
        payloadNew: newPayload,
        createdBy: user.email,
        createdById: user.userId,
      });

      await historicRepo.save(createPartnerActivity);

      Object.assign(existing, {
        ...dto,
        updatedAt: new Date(),
      });

      await settingsRepo.save(existing);

      return existing;
    });
  }
}

import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Exclude } from "class-transformer";

@Entity({ schema: "utils", name: "region" })
export class Region {
  @ApiProperty({
    description: "Identificador da região.",
    type: "string",
    example: "123e4567-e89b-12d3-a456-************",
    required: true,
  })
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ApiProperty({
    description: "Nome da região.",
    type: "string",
    example: "Europe",
    required: true,
  })
  @Column({ name: "name" })
  name: string;

  @ApiProperty({
    description: "Código da moeda.",
    type: "string",
    example: "BRL",
    required: false,
  })
  @Column({ name: "currency_code", type: "varchar", nullable: true })
  currencyCode: string;

  @ApiProperty({
    description: "Símbolo da moeda.",
    type: "string",
    example: "R$",
    required: false,
  })
  @Column({ name: "currency_symbol", type: "varchar", nullable: true })
  currencySymbol: string;

  @ApiProperty({
    description: "Nome da moeda.",
    type: "string",
    example: "Real",
    required: false,
  })
  @Column({ name: "currency_name", type: "varchar", nullable: true })
  currencyName: string;

  @ApiProperty({
    description: "Data de criação",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: true,
  })
  @CreateDateColumn({
    name: "created_at",
    type: "timestamptz",
    default: () => "now()",
  })
  createdAt: Date;

  @ApiProperty({
    description: "Data de atualização",
    type: "string",
    format: "date-time",
    example: "2023-10-01T00:00:00Z",
    required: false,
  })
  @UpdateDateColumn({
    name: "updated_at",
    type: "timestamptz",
    default: () => "now()",
  })
  updatedAt: Date;

  @Exclude()
  @DeleteDateColumn({
    name: "deleted_at",
    type: "timestamptz",
    default: () => "now()",
  })
  deletedAt: Date;

  @Exclude()
  @Column({ name: "is_deleted" })
  isDeleted: boolean;
}

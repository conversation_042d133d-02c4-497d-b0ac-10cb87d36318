import { Controller, Get, Param, HttpCode } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RegionService } from './region.service';
import { Region } from './entities/region.entity';

@ApiTags('Region')
@Controller('regions')
export class RegionController {
  constructor(private readonly regionService: RegionService) { }

  @Get(':id')
  @ApiOperation({ summary: 'Busca as regiões pelo seu identificador.' })
  @ApiResponse({ status: 200, description: 'Retorna a regiao pelo seu identificador.', type: Region })
  @ApiParam({ name: 'id', type: String, description: 'Identificador da região' })
  findById(@Param('id') name: string): Promise<Region> {
    return this.regionService.findById(name);
  }

  @Get('/search/:name')
  @ApiOperation({ summary: 'Busca as regiões por nome.' })
  @ApiResponse({ status: 200, description: 'Retorna lista de regiões por nome.', type: Region })
  @ApiParam({ name: 'name', type: String, description: 'Nome da região' })
  findByName(@Param('name') name: string): Promise<Region[]> {
    return this.regionService.findByName(name);
  }

  @Get()
  @ApiOperation({ summary: 'Busca todas as regiões.' })
  @ApiResponse({ status: 200, description: 'Retorna todas as regiões.', type: Region })
  findAll(): Promise<Region[]> {
    return this.regionService.findAll();
  }

}

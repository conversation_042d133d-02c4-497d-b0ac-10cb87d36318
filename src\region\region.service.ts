import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { InjectEntityManager } from "@nestjs/typeorm";
import { EntityManager, ILike, Like } from "typeorm";
import { Region } from "./entities/region.entity";

@Injectable()
export class RegionService {
  private readonly logger = new Logger(RegionService.name);
  constructor(
    @InjectEntityManager() private readonly utilsManager: EntityManager
  ) {}

  async findById(id: string): Promise<Region> {
    try {
      this.logger.log(`[Start] Find by id region: ${id}`);
      const region = await this.utilsManager.findOne(Region, { where: { id } });

      if (!region) {
        this.logger.warn(`No records found`);
        throw new HttpException("No records found", HttpStatus.NO_CONTENT);
      }

      this.logger.log(`[End] Find by region by id: ${id}`);
      return region;
    } catch (err) {
      this.logger.error(`Error finding region: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async findByName(name: string): Promise<Region[]> {
    try {
      this.logger.log(`[Start] Find by name region: ${name}`);
      const regions = await this.utilsManager.find(Region, {
        where: {
          name: ILike(`%${name}%`),
        },
      });

      if (!regions) {
        this.logger.warn(`No records found`);
        throw new HttpException("No records found", HttpStatus.NO_CONTENT);
      }

      this.logger.log(`[End] Find by region by name: ${name}`);
      return regions;
    } catch (err) {
      this.logger.error(`Error finding region: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }

  async findAll(): Promise<Region[]> {
    try {
      this.logger.log(`[Start] Find regions`);
      const regions = await this.utilsManager.find(Region, {});

      if (!regions) {
        this.logger.warn(`No records found`);
        throw new HttpException("No records found", HttpStatus.NO_CONTENT);
      }

      this.logger.log(`[End] Find by regions:`);
      return regions;
    } catch (err) {
      this.logger.error(`Error finding region: ${err}`);
      throw new HttpException(err.message, err.status);
    }
  }
}
